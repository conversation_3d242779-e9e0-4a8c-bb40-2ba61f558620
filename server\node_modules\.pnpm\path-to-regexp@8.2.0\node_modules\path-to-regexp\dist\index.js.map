{"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["../src/index.ts"], "names": [], "mappings": ";;;AAoRA,sBA6CC;AAKD,0BAgBC;AAgHD,sBA+BC;AAED,oCA+BC;AAsFD,8BAiBC;AA7mBD,MAAM,iBAAiB,GAAG,GAAG,CAAC;AAC9B,MAAM,UAAU,GAAG,CAAC,KAAa,EAAE,EAAE,CAAC,KAAK,CAAC;AAC5C,MAAM,QAAQ,GAAG,qBAAqB,CAAC;AACvC,MAAM,WAAW,GAAG,mCAAmC,CAAC;AACxD,MAAM,SAAS,GAAG,mCAAmC,CAAC;AAkFtD,MAAM,aAAa,GAA8B;IAC/C,UAAU;IACV,GAAG,EAAE,GAAG;IACR,GAAG,EAAE,GAAG;IACR,YAAY;IACZ,GAAG,EAAE,GAAG;IACR,GAAG,EAAE,GAAG;IACR,GAAG,EAAE,GAAG;IACR,GAAG,EAAE,GAAG;IACR,GAAG,EAAE,GAAG;IACR,GAAG,EAAE,GAAG;IACR,GAAG,EAAE,GAAG;CACT,CAAC;AAEF;;GAEG;AACH,SAAS,UAAU,CAAC,GAAW;IAC7B,OAAO,GAAG,CAAC,OAAO,CAAC,kBAAkB,EAAE,MAAM,CAAC,CAAC;AACjD,CAAC;AAED;;GAEG;AACH,SAAS,MAAM,CAAC,GAAW;IACzB,OAAO,GAAG,CAAC,OAAO,CAAC,sBAAsB,EAAE,MAAM,CAAC,CAAC;AACrD,CAAC;AAED;;GAEG;AACH,QAAQ,CAAC,CAAC,KAAK,CAAC,GAAW;IACzB,MAAM,KAAK,GAAG,CAAC,GAAG,GAAG,CAAC,CAAC;IACvB,IAAI,CAAC,GAAG,CAAC,CAAC;IAEV,SAAS,IAAI;QACX,IAAI,KAAK,GAAG,EAAE,CAAC;QAEf,IAAI,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC;YAC9B,KAAK,IAAI,KAAK,CAAC,CAAC,CAAC,CAAC;YAClB,OAAO,WAAW,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC;gBACpC,KAAK,IAAI,KAAK,CAAC,CAAC,CAAC,CAAC;YACpB,CAAC;QACH,CAAC;aAAM,IAAI,KAAK,CAAC,CAAC,CAAC,KAAK,GAAG,EAAE,CAAC;YAC5B,IAAI,GAAG,GAAG,CAAC,CAAC;YAEZ,OAAO,CAAC,GAAG,KAAK,CAAC,MAAM,EAAE,CAAC;gBACxB,IAAI,KAAK,CAAC,EAAE,CAAC,CAAC,KAAK,GAAG,EAAE,CAAC;oBACvB,CAAC,EAAE,CAAC;oBACJ,GAAG,GAAG,CAAC,CAAC;oBACR,MAAM;gBACR,CAAC;gBAED,IAAI,KAAK,CAAC,CAAC,CAAC,KAAK,IAAI,EAAE,CAAC;oBACtB,KAAK,IAAI,KAAK,CAAC,EAAE,CAAC,CAAC,CAAC;gBACtB,CAAC;qBAAM,CAAC;oBACN,KAAK,IAAI,KAAK,CAAC,CAAC,CAAC,CAAC;gBACpB,CAAC;YACH,CAAC;YAED,IAAI,GAAG,EAAE,CAAC;gBACR,MAAM,IAAI,SAAS,CAAC,yBAAyB,GAAG,KAAK,SAAS,EAAE,CAAC,CAAC;YACpE,CAAC;QACH,CAAC;QAED,IAAI,CAAC,KAAK,EAAE,CAAC;YACX,MAAM,IAAI,SAAS,CAAC,6BAA6B,CAAC,KAAK,SAAS,EAAE,CAAC,CAAC;QACtE,CAAC;QAED,OAAO,KAAK,CAAC;IACf,CAAC;IAED,OAAO,CAAC,GAAG,KAAK,CAAC,MAAM,EAAE,CAAC;QACxB,MAAM,KAAK,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;QACvB,MAAM,IAAI,GAAG,aAAa,CAAC,KAAK,CAAC,CAAC;QAElC,IAAI,IAAI,EAAE,CAAC;YACT,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,CAAC,EAAE,EAAE,KAAK,EAAE,CAAC;QACpC,CAAC;aAAM,IAAI,KAAK,KAAK,IAAI,EAAE,CAAC;YAC1B,MAAM,EAAE,IAAI,EAAE,SAAS,EAAE,KAAK,EAAE,CAAC,EAAE,EAAE,KAAK,EAAE,KAAK,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC;QAC3D,CAAC;aAAM,IAAI,KAAK,KAAK,GAAG,EAAE,CAAC;YACzB,MAAM,KAAK,GAAG,IAAI,EAAE,CAAC;YACrB,MAAM,EAAE,IAAI,EAAE,OAAO,EAAE,KAAK,EAAE,CAAC,EAAE,KAAK,EAAE,CAAC;QAC3C,CAAC;aAAM,IAAI,KAAK,KAAK,GAAG,EAAE,CAAC;YACzB,MAAM,KAAK,GAAG,IAAI,EAAE,CAAC;YACrB,MAAM,EAAE,IAAI,EAAE,UAAU,EAAE,KAAK,EAAE,CAAC,EAAE,KAAK,EAAE,CAAC;QAC9C,CAAC;aAAM,CAAC;YACN,MAAM,EAAE,IAAI,EAAE,MAAM,EAAE,KAAK,EAAE,CAAC,EAAE,KAAK,EAAE,KAAK,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC;QACtD,CAAC;IACH,CAAC;IAED,OAAO,EAAE,IAAI,EAAE,KAAK,EAAE,KAAK,EAAE,CAAC,EAAE,KAAK,EAAE,EAAE,EAAE,CAAC;AAC9C,CAAC;AAED,MAAM,IAAI;IAGR,YAAoB,MAAqC;QAArC,WAAM,GAAN,MAAM,CAA+B;IAAG,CAAC;IAE7D,IAAI;QACF,IAAI,CAAC,IAAI,CAAC,KAAK,EAAE,CAAC;YAChB,MAAM,IAAI,GAAG,IAAI,CAAC,MAAM,CAAC,IAAI,EAAE,CAAC;YAChC,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC;QAC1B,CAAC;QACD,OAAO,IAAI,CAAC,KAAK,CAAC;IACpB,CAAC;IAED,UAAU,CAAC,IAAe;QACxB,MAAM,KAAK,GAAG,IAAI,CAAC,IAAI,EAAE,CAAC;QAC1B,IAAI,KAAK,CAAC,IAAI,KAAK,IAAI;YAAE,OAAO;QAChC,IAAI,CAAC,KAAK,GAAG,SAAS,CAAC,CAAC,wBAAwB;QAChD,OAAO,KAAK,CAAC,KAAK,CAAC;IACrB,CAAC;IAED,OAAO,CAAC,IAAe;QACrB,MAAM,KAAK,GAAG,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;QACpC,IAAI,KAAK,KAAK,SAAS;YAAE,OAAO,KAAK,CAAC;QACtC,MAAM,EAAE,IAAI,EAAE,QAAQ,EAAE,KAAK,EAAE,GAAG,IAAI,CAAC,IAAI,EAAE,CAAC;QAC9C,MAAM,IAAI,SAAS,CACjB,cAAc,QAAQ,OAAO,KAAK,cAAc,IAAI,KAAK,SAAS,EAAE,CACrE,CAAC;IACJ,CAAC;IAED,IAAI;QACF,IAAI,MAAM,GAAG,EAAE,CAAC;QAChB,IAAI,KAAyB,CAAC;QAC9B,OAAO,CAAC,KAAK,GAAG,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,IAAI,IAAI,CAAC,UAAU,CAAC,SAAS,CAAC,CAAC,EAAE,CAAC;YACvE,MAAM,IAAI,KAAK,CAAC;QAClB,CAAC;QACD,OAAO,MAAM,CAAC;IAChB,CAAC;CACF;AAiDD;;GAEG;AACH,MAAa,SAAS;IACpB,YAA4B,MAAe;QAAf,WAAM,GAAN,MAAM,CAAS;IAAG,CAAC;CAChD;AAFD,8BAEC;AAED;;GAEG;AACH,SAAgB,KAAK,CAAC,GAAW,EAAE,UAAwB,EAAE;IAC3D,MAAM,EAAE,UAAU,GAAG,UAAU,EAAE,GAAG,OAAO,CAAC;IAC5C,MAAM,EAAE,GAAG,IAAI,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC;IAEhC,SAAS,OAAO,CAAC,OAAkB;QACjC,MAAM,MAAM,GAAY,EAAE,CAAC;QAE3B,OAAO,IAAI,EAAE,CAAC;YACZ,MAAM,IAAI,GAAG,EAAE,CAAC,IAAI,EAAE,CAAC;YACvB,IAAI,IAAI;gBAAE,MAAM,CAAC,IAAI,CAAC,EAAE,IAAI,EAAE,MAAM,EAAE,KAAK,EAAE,UAAU,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;YAEjE,MAAM,KAAK,GAAG,EAAE,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC;YACrC,IAAI,KAAK,EAAE,CAAC;gBACV,MAAM,CAAC,IAAI,CAAC;oBACV,IAAI,EAAE,OAAO;oBACb,IAAI,EAAE,KAAK;iBACZ,CAAC,CAAC;gBACH,SAAS;YACX,CAAC;YAED,MAAM,QAAQ,GAAG,EAAE,CAAC,UAAU,CAAC,UAAU,CAAC,CAAC;YAC3C,IAAI,QAAQ,EAAE,CAAC;gBACb,MAAM,CAAC,IAAI,CAAC;oBACV,IAAI,EAAE,UAAU;oBAChB,IAAI,EAAE,QAAQ;iBACf,CAAC,CAAC;gBACH,SAAS;YACX,CAAC;YAED,MAAM,IAAI,GAAG,EAAE,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC;YAChC,IAAI,IAAI,EAAE,CAAC;gBACT,MAAM,CAAC,IAAI,CAAC;oBACV,IAAI,EAAE,OAAO;oBACb,MAAM,EAAE,OAAO,CAAC,GAAG,CAAC;iBACrB,CAAC,CAAC;gBACH,SAAS;YACX,CAAC;YAED,EAAE,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;YACpB,OAAO,MAAM,CAAC;QAChB,CAAC;IACH,CAAC;IAED,MAAM,MAAM,GAAG,OAAO,CAAC,KAAK,CAAC,CAAC;IAC9B,OAAO,IAAI,SAAS,CAAC,MAAM,CAAC,CAAC;AAC/B,CAAC;AAED;;GAEG;AACH,SAAgB,OAAO,CACrB,IAAU,EACV,UAAyC,EAAE;IAE3C,MAAM,EAAE,MAAM,GAAG,kBAAkB,EAAE,SAAS,GAAG,iBAAiB,EAAE,GAClE,OAAO,CAAC;IACV,MAAM,IAAI,GAAG,IAAI,YAAY,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;IACrE,MAAM,EAAE,GAAG,gBAAgB,CAAC,IAAI,CAAC,MAAM,EAAE,SAAS,EAAE,MAAM,CAAC,CAAC;IAE5D,OAAO,SAAS,IAAI,CAAC,OAAU,EAAO;QACpC,MAAM,CAAC,IAAI,EAAE,GAAG,OAAO,CAAC,GAAG,EAAE,CAAC,IAAI,CAAC,CAAC;QACpC,IAAI,OAAO,CAAC,MAAM,EAAE,CAAC;YACnB,MAAM,IAAI,SAAS,CAAC,uBAAuB,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;QACnE,CAAC;QACD,OAAO,IAAI,CAAC;IACd,CAAC,CAAC;AACJ,CAAC;AAKD,SAAS,gBAAgB,CACvB,MAAe,EACf,SAAiB,EACjB,MAAsB;IAEtB,MAAM,QAAQ,GAAG,MAAM,CAAC,GAAG,CAAC,CAAC,KAAK,EAAE,EAAE,CACpC,eAAe,CAAC,KAAK,EAAE,SAAS,EAAE,MAAM,CAAC,CAC1C,CAAC;IAEF,OAAO,CAAC,IAAe,EAAE,EAAE;QACzB,MAAM,MAAM,GAAa,CAAC,EAAE,CAAC,CAAC;QAE9B,KAAK,MAAM,OAAO,IAAI,QAAQ,EAAE,CAAC;YAC/B,MAAM,CAAC,KAAK,EAAE,GAAG,MAAM,CAAC,GAAG,OAAO,CAAC,IAAI,CAAC,CAAC;YACzC,MAAM,CAAC,CAAC,CAAC,IAAI,KAAK,CAAC;YACnB,MAAM,CAAC,IAAI,CAAC,GAAG,MAAM,CAAC,CAAC;QACzB,CAAC;QAED,OAAO,MAAM,CAAC;IAChB,CAAC,CAAC;AACJ,CAAC;AAED;;GAEG;AACH,SAAS,eAAe,CACtB,KAAY,EACZ,SAAiB,EACjB,MAAsB;IAEtB,IAAI,KAAK,CAAC,IAAI,KAAK,MAAM;QAAE,OAAO,GAAG,EAAE,CAAC,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;IAEtD,IAAI,KAAK,CAAC,IAAI,KAAK,OAAO,EAAE,CAAC;QAC3B,MAAM,EAAE,GAAG,gBAAgB,CAAC,KAAK,CAAC,MAAM,EAAE,SAAS,EAAE,MAAM,CAAC,CAAC;QAE7D,OAAO,CAAC,IAAI,EAAE,EAAE;YACd,MAAM,CAAC,KAAK,EAAE,GAAG,OAAO,CAAC,GAAG,EAAE,CAAC,IAAI,CAAC,CAAC;YACrC,IAAI,CAAC,OAAO,CAAC,MAAM;gBAAE,OAAO,CAAC,KAAK,CAAC,CAAC;YACpC,OAAO,CAAC,EAAE,CAAC,CAAC;QACd,CAAC,CAAC;IACJ,CAAC;IAED,MAAM,WAAW,GAAG,MAAM,IAAI,UAAU,CAAC;IAEzC,IAAI,KAAK,CAAC,IAAI,KAAK,UAAU,IAAI,MAAM,KAAK,KAAK,EAAE,CAAC;QAClD,OAAO,CAAC,IAAI,EAAE,EAAE;YACd,MAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;YAC/B,IAAI,KAAK,IAAI,IAAI;gBAAE,OAAO,CAAC,EAAE,EAAE,KAAK,CAAC,IAAI,CAAC,CAAC;YAE3C,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,IAAI,KAAK,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;gBAChD,MAAM,IAAI,SAAS,CAAC,aAAa,KAAK,CAAC,IAAI,2BAA2B,CAAC,CAAC;YAC1E,CAAC;YAED,OAAO;gBACL,KAAK;qBACF,GAAG,CAAC,CAAC,KAAK,EAAE,KAAK,EAAE,EAAE;oBACpB,IAAI,OAAO,KAAK,KAAK,QAAQ,EAAE,CAAC;wBAC9B,MAAM,IAAI,SAAS,CACjB,aAAa,KAAK,CAAC,IAAI,IAAI,KAAK,kBAAkB,CACnD,CAAC;oBACJ,CAAC;oBAED,OAAO,WAAW,CAAC,KAAK,CAAC,CAAC;gBAC5B,CAAC,CAAC;qBACD,IAAI,CAAC,SAAS,CAAC;aACnB,CAAC;QACJ,CAAC,CAAC;IACJ,CAAC;IAED,OAAO,CAAC,IAAI,EAAE,EAAE;QACd,MAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;QAC/B,IAAI,KAAK,IAAI,IAAI;YAAE,OAAO,CAAC,EAAE,EAAE,KAAK,CAAC,IAAI,CAAC,CAAC;QAE3C,IAAI,OAAO,KAAK,KAAK,QAAQ,EAAE,CAAC;YAC9B,MAAM,IAAI,SAAS,CAAC,aAAa,KAAK,CAAC,IAAI,kBAAkB,CAAC,CAAC;QACjE,CAAC;QAED,OAAO,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC,CAAC;IAC9B,CAAC,CAAC;AACJ,CAAC;AAyBD;;GAEG;AACH,SAAgB,KAAK,CACnB,IAAmB,EACnB,UAAuC,EAAE;IAEzC,MAAM,EAAE,MAAM,GAAG,kBAAkB,EAAE,SAAS,GAAG,iBAAiB,EAAE,GAClE,OAAO,CAAC;IACV,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,GAAG,YAAY,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;IAErD,MAAM,QAAQ,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,GAAG,EAAE,EAAE;QAChC,IAAI,MAAM,KAAK,KAAK;YAAE,OAAO,UAAU,CAAC;QACxC,IAAI,GAAG,CAAC,IAAI,KAAK,OAAO;YAAE,OAAO,MAAM,CAAC;QACxC,OAAO,CAAC,KAAa,EAAE,EAAE,CAAC,KAAK,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;IAC/D,CAAC,CAAC,CAAC;IAEH,OAAO,SAAS,KAAK,CAAC,KAAa;QACjC,MAAM,CAAC,GAAG,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAC7B,IAAI,CAAC,CAAC;YAAE,OAAO,KAAK,CAAC;QAErB,MAAM,IAAI,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;QAClB,MAAM,MAAM,GAAG,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;QAEnC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;YAClC,IAAI,CAAC,CAAC,CAAC,CAAC,KAAK,SAAS;gBAAE,SAAS;YAEjC,MAAM,GAAG,GAAG,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;YACxB,MAAM,OAAO,GAAG,QAAQ,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;YAChC,MAAM,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACnC,CAAC;QAED,OAAO,EAAE,IAAI,EAAE,MAAM,EAAE,CAAC;IAC1B,CAAC,CAAC;AACJ,CAAC;AAED,SAAgB,YAAY,CAC1B,IAAmB,EACnB,UAA8C,EAAE;IAEhD,MAAM,EACJ,SAAS,GAAG,iBAAiB,EAC7B,GAAG,GAAG,IAAI,EACV,SAAS,GAAG,KAAK,EACjB,QAAQ,GAAG,IAAI,GAChB,GAAG,OAAO,CAAC;IACZ,MAAM,IAAI,GAAS,EAAE,CAAC;IACtB,MAAM,OAAO,GAAa,EAAE,CAAC;IAC7B,MAAM,KAAK,GAAG,SAAS,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC;IACnC,MAAM,KAAK,GAAG,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC;IAClD,MAAM,KAAK,GAAG,KAAK,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,EAAE,CAC/B,IAAI,YAAY,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK,CAAC,IAAI,EAAE,OAAO,CAAC,CACxD,CAAC;IAEF,KAAK,MAAM,EAAE,MAAM,EAAE,IAAI,KAAK,EAAE,CAAC;QAC/B,KAAK,MAAM,GAAG,IAAI,OAAO,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC;YACzC,MAAM,MAAM,GAAG,gBAAgB,CAAC,GAAG,EAAE,SAAS,EAAE,IAAI,CAAC,CAAC;YACtD,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;QACvB,CAAC;IACH,CAAC;IAED,IAAI,OAAO,GAAG,OAAO,OAAO,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC;IAC1C,IAAI,QAAQ;QAAE,OAAO,IAAI,MAAM,MAAM,CAAC,SAAS,CAAC,KAAK,CAAC;IACtD,OAAO,IAAI,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,MAAM,MAAM,CAAC,SAAS,CAAC,KAAK,CAAC;IAEpD,MAAM,MAAM,GAAG,IAAI,MAAM,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC;IAC1C,OAAO,EAAE,MAAM,EAAE,IAAI,EAAE,CAAC;AAC1B,CAAC;AAOD;;GAEG;AACH,QAAQ,CAAC,CAAC,OAAO,CACf,MAAe,EACf,KAAa,EACb,IAAiB;IAEjB,IAAI,KAAK,KAAK,MAAM,CAAC,MAAM,EAAE,CAAC;QAC5B,OAAO,MAAM,IAAI,CAAC;IACpB,CAAC;IAED,MAAM,KAAK,GAAG,MAAM,CAAC,KAAK,CAAC,CAAC;IAE5B,IAAI,KAAK,CAAC,IAAI,KAAK,OAAO,EAAE,CAAC;QAC3B,MAAM,IAAI,GAAG,IAAI,CAAC,KAAK,EAAE,CAAC;QAC1B,KAAK,MAAM,GAAG,IAAI,OAAO,CAAC,KAAK,CAAC,MAAM,EAAE,CAAC,EAAE,IAAI,CAAC,EAAE,CAAC;YACjD,KAAK,CAAC,CAAC,OAAO,CAAC,MAAM,EAAE,KAAK,GAAG,CAAC,EAAE,GAAG,CAAC,CAAC;QACzC,CAAC;IACH,CAAC;SAAM,CAAC;QACN,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;IACnB,CAAC;IAED,KAAK,CAAC,CAAC,OAAO,CAAC,MAAM,EAAE,KAAK,GAAG,CAAC,EAAE,IAAI,CAAC,CAAC;AAC1C,CAAC;AAED;;GAEG;AACH,SAAS,gBAAgB,CAAC,MAAmB,EAAE,SAAiB,EAAE,IAAU;IAC1E,IAAI,MAAM,GAAG,EAAE,CAAC;IAChB,IAAI,SAAS,GAAG,EAAE,CAAC;IACnB,IAAI,kBAAkB,GAAG,IAAI,CAAC;IAE9B,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,MAAM,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;QACvC,MAAM,KAAK,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC;QAExB,IAAI,KAAK,CAAC,IAAI,KAAK,MAAM,EAAE,CAAC;YAC1B,MAAM,IAAI,MAAM,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;YAC9B,SAAS,IAAI,KAAK,CAAC,KAAK,CAAC;YACzB,kBAAkB,KAAlB,kBAAkB,GAAK,KAAK,CAAC,KAAK,CAAC,QAAQ,CAAC,SAAS,CAAC,EAAC;YACvD,SAAS;QACX,CAAC;QAED,IAAI,KAAK,CAAC,IAAI,KAAK,OAAO,IAAI,KAAK,CAAC,IAAI,KAAK,UAAU,EAAE,CAAC;YACxD,IAAI,CAAC,kBAAkB,IAAI,CAAC,SAAS,EAAE,CAAC;gBACtC,MAAM,IAAI,SAAS,CAAC,uBAAuB,KAAK,CAAC,IAAI,MAAM,SAAS,EAAE,CAAC,CAAC;YAC1E,CAAC;YAED,IAAI,KAAK,CAAC,IAAI,KAAK,OAAO,EAAE,CAAC;gBAC3B,MAAM,IAAI,IAAI,MAAM,CAAC,SAAS,EAAE,kBAAkB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,SAAS,CAAC,IAAI,CAAC;YAC3E,CAAC;iBAAM,CAAC;gBACN,MAAM,IAAI,aAAa,CAAC;YAC1B,CAAC;YAED,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YACjB,SAAS,GAAG,EAAE,CAAC;YACf,kBAAkB,GAAG,KAAK,CAAC;YAC3B,SAAS;QACX,CAAC;IACH,CAAC;IAED,OAAO,MAAM,CAAC;AAChB,CAAC;AAED,SAAS,MAAM,CAAC,SAAiB,EAAE,SAAiB;IAClD,IAAI,SAAS,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;QACzB,IAAI,SAAS,CAAC,MAAM,GAAG,CAAC;YAAE,OAAO,KAAK,MAAM,CAAC,SAAS,GAAG,SAAS,CAAC,GAAG,CAAC;QACvE,OAAO,SAAS,MAAM,CAAC,SAAS,CAAC,MAAM,MAAM,CAAC,SAAS,CAAC,IAAI,CAAC;IAC/D,CAAC;IACD,IAAI,SAAS,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;QACzB,OAAO,SAAS,MAAM,CAAC,SAAS,CAAC,MAAM,MAAM,CAAC,SAAS,CAAC,IAAI,CAAC;IAC/D,CAAC;IACD,OAAO,SAAS,MAAM,CAAC,SAAS,CAAC,IAAI,MAAM,CAAC,SAAS,CAAC,YAAY,CAAC;AACrE,CAAC;AAED;;GAEG;AACH,SAAgB,SAAS,CAAC,IAAe;IACvC,OAAO,IAAI,CAAC,MAAM;SACf,GAAG,CAAC,SAAS,cAAc,CAAC,KAAK,EAAE,KAAK,EAAE,MAAM;QAC/C,IAAI,KAAK,CAAC,IAAI,KAAK,MAAM;YAAE,OAAO,UAAU,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;QAC1D,IAAI,KAAK,CAAC,IAAI,KAAK,OAAO,EAAE,CAAC;YAC3B,OAAO,IAAI,KAAK,CAAC,MAAM,CAAC,GAAG,CAAC,cAAc,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,GAAG,CAAC;QAC1D,CAAC;QAED,MAAM,MAAM,GACV,UAAU,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,cAAc,CAAC,MAAM,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC,CAAC;QAC9D,MAAM,GAAG,GAAG,MAAM,CAAC,CAAC,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;QAE7D,IAAI,KAAK,CAAC,IAAI,KAAK,OAAO;YAAE,OAAO,IAAI,GAAG,EAAE,CAAC;QAC7C,IAAI,KAAK,CAAC,IAAI,KAAK,UAAU;YAAE,OAAO,IAAI,GAAG,EAAE,CAAC;QAChD,MAAM,IAAI,SAAS,CAAC,qBAAqB,KAAK,EAAE,CAAC,CAAC;IACpD,CAAC,CAAC;SACD,IAAI,CAAC,EAAE,CAAC,CAAC;AACd,CAAC;AAED,SAAS,UAAU,CAAC,IAAY;IAC9B,MAAM,CAAC,KAAK,EAAE,GAAG,IAAI,CAAC,GAAG,IAAI,CAAC;IAC9B,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC;QAAE,OAAO,KAAK,CAAC;IACxC,OAAO,IAAI,CAAC,KAAK,CAAC,CAAC,IAAI,EAAE,EAAE,CAAC,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;AACtD,CAAC;AAED,SAAS,cAAc,CAAC,KAAwB;IAC9C,IAAI,CAAA,KAAK,aAAL,KAAK,uBAAL,KAAK,CAAE,IAAI,MAAK,MAAM;QAAE,OAAO,IAAI,CAAC;IACxC,OAAO,CAAC,WAAW,CAAC,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;AAC3C,CAAC", "sourcesContent": ["const DEFAULT_DELIMITER = \"/\";\nconst NOOP_VALUE = (value: string) => value;\nconst ID_START = /^[$_\\p{ID_Start}]$/u;\nconst ID_CONTINUE = /^[$\\u200c\\u200d\\p{ID_Continue}]$/u;\nconst DEBUG_URL = \"https://git.new/pathToRegexpError\";\n\n/**\n * Encode a string into another string.\n */\nexport type Encode = (value: string) => string;\n\n/**\n * Decode a string into another string.\n */\nexport type Decode = (value: string) => string;\n\nexport interface ParseOptions {\n  /**\n   * A function for encoding input strings.\n   */\n  encodePath?: Encode;\n}\n\nexport interface PathToRegexpOptions {\n  /**\n   * Matches the path completely without trailing characters. (default: `true`)\n   */\n  end?: boolean;\n  /**\n   * Allows optional trailing delimiter to match. (default: `true`)\n   */\n  trailing?: boolean;\n  /**\n   * Match will be case sensitive. (default: `false`)\n   */\n  sensitive?: boolean;\n  /**\n   * The default delimiter for segments. (default: `'/'`)\n   */\n  delimiter?: string;\n}\n\nexport interface MatchOptions extends PathToRegexpOptions {\n  /**\n   * Function for decoding strings for params, or `false` to disable entirely. (default: `decodeURIComponent`)\n   */\n  decode?: Decode | false;\n}\n\nexport interface CompileOptions {\n  /**\n   * Function for encoding input strings for output into the path, or `false` to disable entirely. (default: `encodeURIComponent`)\n   */\n  encode?: Encode | false;\n  /**\n   * The default delimiter for segments. (default: `'/'`)\n   */\n  delimiter?: string;\n}\n\ntype TokenType =\n  | \"{\"\n  | \"}\"\n  | \"WILDCARD\"\n  | \"PARAM\"\n  | \"CHAR\"\n  | \"ESCAPED\"\n  | \"END\"\n  // Reserved for use or ambiguous due to past use.\n  | \"(\"\n  | \")\"\n  | \"[\"\n  | \"]\"\n  | \"+\"\n  | \"?\"\n  | \"!\";\n\n/**\n * Tokenizer results.\n */\ninterface LexToken {\n  type: TokenType;\n  index: number;\n  value: string;\n}\n\nconst SIMPLE_TOKENS: Record<string, TokenType> = {\n  // Groups.\n  \"{\": \"{\",\n  \"}\": \"}\",\n  // Reserved.\n  \"(\": \"(\",\n  \")\": \")\",\n  \"[\": \"[\",\n  \"]\": \"]\",\n  \"+\": \"+\",\n  \"?\": \"?\",\n  \"!\": \"!\",\n};\n\n/**\n * Escape text for stringify to path.\n */\nfunction escapeText(str: string) {\n  return str.replace(/[{}()\\[\\]+?!:*]/g, \"\\\\$&\");\n}\n\n/**\n * Escape a regular expression string.\n */\nfunction escape(str: string) {\n  return str.replace(/[.+*?^${}()[\\]|/\\\\]/g, \"\\\\$&\");\n}\n\n/**\n * Tokenize input string.\n */\nfunction* lexer(str: string): Generator<LexToken, LexToken> {\n  const chars = [...str];\n  let i = 0;\n\n  function name() {\n    let value = \"\";\n\n    if (ID_START.test(chars[++i])) {\n      value += chars[i];\n      while (ID_CONTINUE.test(chars[++i])) {\n        value += chars[i];\n      }\n    } else if (chars[i] === '\"') {\n      let pos = i;\n\n      while (i < chars.length) {\n        if (chars[++i] === '\"') {\n          i++;\n          pos = 0;\n          break;\n        }\n\n        if (chars[i] === \"\\\\\") {\n          value += chars[++i];\n        } else {\n          value += chars[i];\n        }\n      }\n\n      if (pos) {\n        throw new TypeError(`Unterminated quote at ${pos}: ${DEBUG_URL}`);\n      }\n    }\n\n    if (!value) {\n      throw new TypeError(`Missing parameter name at ${i}: ${DEBUG_URL}`);\n    }\n\n    return value;\n  }\n\n  while (i < chars.length) {\n    const value = chars[i];\n    const type = SIMPLE_TOKENS[value];\n\n    if (type) {\n      yield { type, index: i++, value };\n    } else if (value === \"\\\\\") {\n      yield { type: \"ESCAPED\", index: i++, value: chars[i++] };\n    } else if (value === \":\") {\n      const value = name();\n      yield { type: \"PARAM\", index: i, value };\n    } else if (value === \"*\") {\n      const value = name();\n      yield { type: \"WILDCARD\", index: i, value };\n    } else {\n      yield { type: \"CHAR\", index: i, value: chars[i++] };\n    }\n  }\n\n  return { type: \"END\", index: i, value: \"\" };\n}\n\nclass Iter {\n  private _peek?: LexToken;\n\n  constructor(private tokens: Generator<LexToken, LexToken>) {}\n\n  peek(): LexToken {\n    if (!this._peek) {\n      const next = this.tokens.next();\n      this._peek = next.value;\n    }\n    return this._peek;\n  }\n\n  tryConsume(type: TokenType): string | undefined {\n    const token = this.peek();\n    if (token.type !== type) return;\n    this._peek = undefined; // Reset after consumed.\n    return token.value;\n  }\n\n  consume(type: TokenType): string {\n    const value = this.tryConsume(type);\n    if (value !== undefined) return value;\n    const { type: nextType, index } = this.peek();\n    throw new TypeError(\n      `Unexpected ${nextType} at ${index}, expected ${type}: ${DEBUG_URL}`,\n    );\n  }\n\n  text(): string {\n    let result = \"\";\n    let value: string | undefined;\n    while ((value = this.tryConsume(\"CHAR\") || this.tryConsume(\"ESCAPED\"))) {\n      result += value;\n    }\n    return result;\n  }\n}\n\n/**\n * Plain text.\n */\nexport interface Text {\n  type: \"text\";\n  value: string;\n}\n\n/**\n * A parameter designed to match arbitrary text within a segment.\n */\nexport interface Parameter {\n  type: \"param\";\n  name: string;\n}\n\n/**\n * A wildcard parameter designed to match multiple segments.\n */\nexport interface Wildcard {\n  type: \"wildcard\";\n  name: string;\n}\n\n/**\n * A set of possible tokens to expand when matching.\n */\nexport interface Group {\n  type: \"group\";\n  tokens: Token[];\n}\n\n/**\n * A token that corresponds with a regexp capture.\n */\nexport type Key = Parameter | Wildcard;\n\n/**\n * A sequence of `path-to-regexp` keys that match capturing groups.\n */\nexport type Keys = Array<Key>;\n\n/**\n * A sequence of path match characters.\n */\nexport type Token = Text | Parameter | Wildcard | Group;\n\n/**\n * Tokenized path instance.\n */\nexport class TokenData {\n  constructor(public readonly tokens: Token[]) {}\n}\n\n/**\n * Parse a string for the raw tokens.\n */\nexport function parse(str: string, options: ParseOptions = {}): TokenData {\n  const { encodePath = NOOP_VALUE } = options;\n  const it = new Iter(lexer(str));\n\n  function consume(endType: TokenType): Token[] {\n    const tokens: Token[] = [];\n\n    while (true) {\n      const path = it.text();\n      if (path) tokens.push({ type: \"text\", value: encodePath(path) });\n\n      const param = it.tryConsume(\"PARAM\");\n      if (param) {\n        tokens.push({\n          type: \"param\",\n          name: param,\n        });\n        continue;\n      }\n\n      const wildcard = it.tryConsume(\"WILDCARD\");\n      if (wildcard) {\n        tokens.push({\n          type: \"wildcard\",\n          name: wildcard,\n        });\n        continue;\n      }\n\n      const open = it.tryConsume(\"{\");\n      if (open) {\n        tokens.push({\n          type: \"group\",\n          tokens: consume(\"}\"),\n        });\n        continue;\n      }\n\n      it.consume(endType);\n      return tokens;\n    }\n  }\n\n  const tokens = consume(\"END\");\n  return new TokenData(tokens);\n}\n\n/**\n * Compile a string to a template function for the path.\n */\nexport function compile<P extends ParamData = ParamData>(\n  path: Path,\n  options: CompileOptions & ParseOptions = {},\n) {\n  const { encode = encodeURIComponent, delimiter = DEFAULT_DELIMITER } =\n    options;\n  const data = path instanceof TokenData ? path : parse(path, options);\n  const fn = tokensToFunction(data.tokens, delimiter, encode);\n\n  return function path(data: P = {} as P) {\n    const [path, ...missing] = fn(data);\n    if (missing.length) {\n      throw new TypeError(`Missing parameters: ${missing.join(\", \")}`);\n    }\n    return path;\n  };\n}\n\nexport type ParamData = Partial<Record<string, string | string[]>>;\nexport type PathFunction<P extends ParamData> = (data?: P) => string;\n\nfunction tokensToFunction(\n  tokens: Token[],\n  delimiter: string,\n  encode: Encode | false,\n) {\n  const encoders = tokens.map((token) =>\n    tokenToFunction(token, delimiter, encode),\n  );\n\n  return (data: ParamData) => {\n    const result: string[] = [\"\"];\n\n    for (const encoder of encoders) {\n      const [value, ...extras] = encoder(data);\n      result[0] += value;\n      result.push(...extras);\n    }\n\n    return result;\n  };\n}\n\n/**\n * Convert a single token into a path building function.\n */\nfunction tokenToFunction(\n  token: Token,\n  delimiter: string,\n  encode: Encode | false,\n): (data: ParamData) => string[] {\n  if (token.type === \"text\") return () => [token.value];\n\n  if (token.type === \"group\") {\n    const fn = tokensToFunction(token.tokens, delimiter, encode);\n\n    return (data) => {\n      const [value, ...missing] = fn(data);\n      if (!missing.length) return [value];\n      return [\"\"];\n    };\n  }\n\n  const encodeValue = encode || NOOP_VALUE;\n\n  if (token.type === \"wildcard\" && encode !== false) {\n    return (data) => {\n      const value = data[token.name];\n      if (value == null) return [\"\", token.name];\n\n      if (!Array.isArray(value) || value.length === 0) {\n        throw new TypeError(`Expected \"${token.name}\" to be a non-empty array`);\n      }\n\n      return [\n        value\n          .map((value, index) => {\n            if (typeof value !== \"string\") {\n              throw new TypeError(\n                `Expected \"${token.name}/${index}\" to be a string`,\n              );\n            }\n\n            return encodeValue(value);\n          })\n          .join(delimiter),\n      ];\n    };\n  }\n\n  return (data) => {\n    const value = data[token.name];\n    if (value == null) return [\"\", token.name];\n\n    if (typeof value !== \"string\") {\n      throw new TypeError(`Expected \"${token.name}\" to be a string`);\n    }\n\n    return [encodeValue(value)];\n  };\n}\n\n/**\n * A match result contains data about the path match.\n */\nexport interface MatchResult<P extends ParamData> {\n  path: string;\n  params: P;\n}\n\n/**\n * A match is either `false` (no match) or a match result.\n */\nexport type Match<P extends ParamData> = false | MatchResult<P>;\n\n/**\n * The match function takes a string and returns whether it matched the path.\n */\nexport type MatchFunction<P extends ParamData> = (path: string) => Match<P>;\n\n/**\n * Supported path types.\n */\nexport type Path = string | TokenData;\n\n/**\n * Transform a path into a match function.\n */\nexport function match<P extends ParamData>(\n  path: Path | Path[],\n  options: MatchOptions & ParseOptions = {},\n): MatchFunction<P> {\n  const { decode = decodeURIComponent, delimiter = DEFAULT_DELIMITER } =\n    options;\n  const { regexp, keys } = pathToRegexp(path, options);\n\n  const decoders = keys.map((key) => {\n    if (decode === false) return NOOP_VALUE;\n    if (key.type === \"param\") return decode;\n    return (value: string) => value.split(delimiter).map(decode);\n  });\n\n  return function match(input: string) {\n    const m = regexp.exec(input);\n    if (!m) return false;\n\n    const path = m[0];\n    const params = Object.create(null);\n\n    for (let i = 1; i < m.length; i++) {\n      if (m[i] === undefined) continue;\n\n      const key = keys[i - 1];\n      const decoder = decoders[i - 1];\n      params[key.name] = decoder(m[i]);\n    }\n\n    return { path, params };\n  };\n}\n\nexport function pathToRegexp(\n  path: Path | Path[],\n  options: PathToRegexpOptions & ParseOptions = {},\n) {\n  const {\n    delimiter = DEFAULT_DELIMITER,\n    end = true,\n    sensitive = false,\n    trailing = true,\n  } = options;\n  const keys: Keys = [];\n  const sources: string[] = [];\n  const flags = sensitive ? \"\" : \"i\";\n  const paths = Array.isArray(path) ? path : [path];\n  const items = paths.map((path) =>\n    path instanceof TokenData ? path : parse(path, options),\n  );\n\n  for (const { tokens } of items) {\n    for (const seq of flatten(tokens, 0, [])) {\n      const regexp = sequenceToRegExp(seq, delimiter, keys);\n      sources.push(regexp);\n    }\n  }\n\n  let pattern = `^(?:${sources.join(\"|\")})`;\n  if (trailing) pattern += `(?:${escape(delimiter)}$)?`;\n  pattern += end ? \"$\" : `(?=${escape(delimiter)}|$)`;\n\n  const regexp = new RegExp(pattern, flags);\n  return { regexp, keys };\n}\n\n/**\n * Flattened token set.\n */\ntype Flattened = Text | Parameter | Wildcard;\n\n/**\n * Generate a flat list of sequence tokens from the given tokens.\n */\nfunction* flatten(\n  tokens: Token[],\n  index: number,\n  init: Flattened[],\n): Generator<Flattened[]> {\n  if (index === tokens.length) {\n    return yield init;\n  }\n\n  const token = tokens[index];\n\n  if (token.type === \"group\") {\n    const fork = init.slice();\n    for (const seq of flatten(token.tokens, 0, fork)) {\n      yield* flatten(tokens, index + 1, seq);\n    }\n  } else {\n    init.push(token);\n  }\n\n  yield* flatten(tokens, index + 1, init);\n}\n\n/**\n * Transform a flat sequence of tokens into a regular expression.\n */\nfunction sequenceToRegExp(tokens: Flattened[], delimiter: string, keys: Keys) {\n  let result = \"\";\n  let backtrack = \"\";\n  let isSafeSegmentParam = true;\n\n  for (let i = 0; i < tokens.length; i++) {\n    const token = tokens[i];\n\n    if (token.type === \"text\") {\n      result += escape(token.value);\n      backtrack += token.value;\n      isSafeSegmentParam ||= token.value.includes(delimiter);\n      continue;\n    }\n\n    if (token.type === \"param\" || token.type === \"wildcard\") {\n      if (!isSafeSegmentParam && !backtrack) {\n        throw new TypeError(`Missing text after \"${token.name}\": ${DEBUG_URL}`);\n      }\n\n      if (token.type === \"param\") {\n        result += `(${negate(delimiter, isSafeSegmentParam ? \"\" : backtrack)}+)`;\n      } else {\n        result += `([\\\\s\\\\S]+)`;\n      }\n\n      keys.push(token);\n      backtrack = \"\";\n      isSafeSegmentParam = false;\n      continue;\n    }\n  }\n\n  return result;\n}\n\nfunction negate(delimiter: string, backtrack: string) {\n  if (backtrack.length < 2) {\n    if (delimiter.length < 2) return `[^${escape(delimiter + backtrack)}]`;\n    return `(?:(?!${escape(delimiter)})[^${escape(backtrack)}])`;\n  }\n  if (delimiter.length < 2) {\n    return `(?:(?!${escape(backtrack)})[^${escape(delimiter)}])`;\n  }\n  return `(?:(?!${escape(backtrack)}|${escape(delimiter)})[\\\\s\\\\S])`;\n}\n\n/**\n * Stringify token data into a path string.\n */\nexport function stringify(data: TokenData) {\n  return data.tokens\n    .map(function stringifyToken(token, index, tokens): string {\n      if (token.type === \"text\") return escapeText(token.value);\n      if (token.type === \"group\") {\n        return `{${token.tokens.map(stringifyToken).join(\"\")}}`;\n      }\n\n      const isSafe =\n        isNameSafe(token.name) && isNextNameSafe(tokens[index + 1]);\n      const key = isSafe ? token.name : JSON.stringify(token.name);\n\n      if (token.type === \"param\") return `:${key}`;\n      if (token.type === \"wildcard\") return `*${key}`;\n      throw new TypeError(`Unexpected token: ${token}`);\n    })\n    .join(\"\");\n}\n\nfunction isNameSafe(name: string) {\n  const [first, ...rest] = name;\n  if (!ID_START.test(first)) return false;\n  return rest.every((char) => ID_CONTINUE.test(char));\n}\n\nfunction isNextNameSafe(token: Token | undefined) {\n  if (token?.type !== \"text\") return true;\n  return !ID_CONTINUE.test(token.value[0]);\n}\n"]}