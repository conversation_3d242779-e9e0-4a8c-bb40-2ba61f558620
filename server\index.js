import express from 'express';
import logger from 'morgan';
import { StatusCodes } from 'http-status-codes';
import cors from 'cors';
import fs from 'fs-extra';
import path from 'node:path';

const __dirname = path.resolve();

// 存放上传的文件
fs.ensureDirSync(path.resolve(__dirname, 'public'));
// 存放分片的文件
fs.ensureDirSync(path.resolve(__dirname, 'temp'));

// 配置multer用于处理文件上传
const storage = multer.diskStorage({
  destination: function (req, file, cb) {
    cb(null, path.resolve(__dirname, 'temp'));
  },
  filename: function (req, file, cb) {
    const { chunkFileName } = req.query;
    cb(null, chunkFileName);
  },
});

const upload = multer({ storage: storage });

const app = express();
app.use(logger('dev'));
app.use(cors());
app.use(express.json({ limit: '200mb' }));
app.use(express.urlencoded({ extended: true, limit: '200mb' }));
app.use(express.static(path.join(__dirname, 'public')));

app.post('/upload/:fileName', upload.single('chunk'), async (req, res) => {
  try {
    const { fileName } = req.params;
    const { chunkFileName } = req.query;

    console.log('=== UPLOAD REQUEST ===');
    console.log('chunkFileName:', chunkFileName);
    console.log('fileName:', fileName);
    console.log('file saved to:', req.file?.path);
    console.log('=== SENDING RESPONSE ===');

    res.json({ success: true });
  } catch (error) {
    console.error('Upload error:', error);
    res.status(500).json({ success: false, error: error.message });
  }
});

app.get('/merge/:fileName', async (req, res) => {
  res.json({ success: true });
});

app.listen(8080, () => {
  console.log('Server is running on port 8080');
});
