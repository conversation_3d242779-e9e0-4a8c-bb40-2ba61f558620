hoistPattern:
  - '*'
hoistedDependencies:
  '@ampproject/remapping@2.3.0':
    '@ampproject/remapping': private
  '@ant-design/colors@8.0.0':
    '@ant-design/colors': private
  '@ant-design/cssinjs-utils@1.1.3(react-dom@18.3.1(react@18.3.1))(react@18.3.1)':
    '@ant-design/cssinjs-utils': private
  '@ant-design/cssinjs@1.24.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1)':
    '@ant-design/cssinjs': private
  '@ant-design/fast-color@2.0.6':
    '@ant-design/fast-color': private
  '@ant-design/icons-svg@4.4.2':
    '@ant-design/icons-svg': private
  '@ant-design/react-slick@1.1.2(react@18.3.1)':
    '@ant-design/react-slick': private
  '@babel/code-frame@7.27.1':
    '@babel/code-frame': private
  '@babel/compat-data@7.28.0':
    '@babel/compat-data': private
  '@babel/core@7.28.0':
    '@babel/core': private
  '@babel/generator@7.28.0':
    '@babel/generator': private
  '@babel/helper-compilation-targets@7.27.2':
    '@babel/helper-compilation-targets': private
  '@babel/helper-globals@7.28.0':
    '@babel/helper-globals': private
  '@babel/helper-module-imports@7.27.1':
    '@babel/helper-module-imports': private
  '@babel/helper-module-transforms@7.27.3(@babel/core@7.28.0)':
    '@babel/helper-module-transforms': private
  '@babel/helper-plugin-utils@7.27.1':
    '@babel/helper-plugin-utils': private
  '@babel/helper-string-parser@7.27.1':
    '@babel/helper-string-parser': private
  '@babel/helper-validator-identifier@7.27.1':
    '@babel/helper-validator-identifier': private
  '@babel/helper-validator-option@7.27.1':
    '@babel/helper-validator-option': private
  '@babel/helpers@7.28.2':
    '@babel/helpers': private
  '@babel/parser@7.28.0':
    '@babel/parser': private
  '@babel/plugin-transform-react-jsx-self@7.27.1(@babel/core@7.28.0)':
    '@babel/plugin-transform-react-jsx-self': private
  '@babel/plugin-transform-react-jsx-source@7.27.1(@babel/core@7.28.0)':
    '@babel/plugin-transform-react-jsx-source': private
  '@babel/runtime@7.28.2':
    '@babel/runtime': private
  '@babel/template@7.27.2':
    '@babel/template': private
  '@babel/traverse@7.28.0':
    '@babel/traverse': private
  '@babel/types@7.28.2':
    '@babel/types': private
  '@emotion/hash@0.8.0':
    '@emotion/hash': private
  '@emotion/unitless@0.7.5':
    '@emotion/unitless': private
  '@esbuild/aix-ppc64@0.21.5':
    '@esbuild/aix-ppc64': private
  '@esbuild/android-arm64@0.21.5':
    '@esbuild/android-arm64': private
  '@esbuild/android-arm@0.21.5':
    '@esbuild/android-arm': private
  '@esbuild/android-x64@0.21.5':
    '@esbuild/android-x64': private
  '@esbuild/darwin-arm64@0.21.5':
    '@esbuild/darwin-arm64': private
  '@esbuild/darwin-x64@0.21.5':
    '@esbuild/darwin-x64': private
  '@esbuild/freebsd-arm64@0.21.5':
    '@esbuild/freebsd-arm64': private
  '@esbuild/freebsd-x64@0.21.5':
    '@esbuild/freebsd-x64': private
  '@esbuild/linux-arm64@0.21.5':
    '@esbuild/linux-arm64': private
  '@esbuild/linux-arm@0.21.5':
    '@esbuild/linux-arm': private
  '@esbuild/linux-ia32@0.21.5':
    '@esbuild/linux-ia32': private
  '@esbuild/linux-loong64@0.21.5':
    '@esbuild/linux-loong64': private
  '@esbuild/linux-mips64el@0.21.5':
    '@esbuild/linux-mips64el': private
  '@esbuild/linux-ppc64@0.21.5':
    '@esbuild/linux-ppc64': private
  '@esbuild/linux-riscv64@0.21.5':
    '@esbuild/linux-riscv64': private
  '@esbuild/linux-s390x@0.21.5':
    '@esbuild/linux-s390x': private
  '@esbuild/linux-x64@0.21.5':
    '@esbuild/linux-x64': private
  '@esbuild/netbsd-x64@0.21.5':
    '@esbuild/netbsd-x64': private
  '@esbuild/openbsd-x64@0.21.5':
    '@esbuild/openbsd-x64': private
  '@esbuild/sunos-x64@0.21.5':
    '@esbuild/sunos-x64': private
  '@esbuild/win32-arm64@0.21.5':
    '@esbuild/win32-arm64': private
  '@esbuild/win32-ia32@0.21.5':
    '@esbuild/win32-ia32': private
  '@esbuild/win32-x64@0.21.5':
    '@esbuild/win32-x64': private
  '@jridgewell/gen-mapping@0.3.12':
    '@jridgewell/gen-mapping': private
  '@jridgewell/resolve-uri@3.1.2':
    '@jridgewell/resolve-uri': private
  '@jridgewell/sourcemap-codec@1.5.4':
    '@jridgewell/sourcemap-codec': private
  '@jridgewell/trace-mapping@0.3.29':
    '@jridgewell/trace-mapping': private
  '@rc-component/async-validator@5.0.4':
    '@rc-component/async-validator': private
  '@rc-component/color-picker@2.0.1(react-dom@18.3.1(react@18.3.1))(react@18.3.1)':
    '@rc-component/color-picker': private
  '@rc-component/context@1.4.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1)':
    '@rc-component/context': private
  '@rc-component/mini-decimal@1.1.0':
    '@rc-component/mini-decimal': private
  '@rc-component/mutate-observer@1.1.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1)':
    '@rc-component/mutate-observer': private
  '@rc-component/portal@1.1.2(react-dom@18.3.1(react@18.3.1))(react@18.3.1)':
    '@rc-component/portal': private
  '@rc-component/qrcode@1.0.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1)':
    '@rc-component/qrcode': private
  '@rc-component/tour@1.15.1(react-dom@18.3.1(react@18.3.1))(react@18.3.1)':
    '@rc-component/tour': private
  '@rc-component/trigger@2.3.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1)':
    '@rc-component/trigger': private
  '@rc-component/util@1.2.2(react-dom@18.3.1(react@18.3.1))(react@18.3.1)':
    '@rc-component/util': private
  '@rolldown/pluginutils@1.0.0-beta.27':
    '@rolldown/pluginutils': private
  '@rollup/rollup-android-arm-eabi@4.46.2':
    '@rollup/rollup-android-arm-eabi': private
  '@rollup/rollup-android-arm64@4.46.2':
    '@rollup/rollup-android-arm64': private
  '@rollup/rollup-darwin-arm64@4.46.2':
    '@rollup/rollup-darwin-arm64': private
  '@rollup/rollup-darwin-x64@4.46.2':
    '@rollup/rollup-darwin-x64': private
  '@rollup/rollup-freebsd-arm64@4.46.2':
    '@rollup/rollup-freebsd-arm64': private
  '@rollup/rollup-freebsd-x64@4.46.2':
    '@rollup/rollup-freebsd-x64': private
  '@rollup/rollup-linux-arm-gnueabihf@4.46.2':
    '@rollup/rollup-linux-arm-gnueabihf': private
  '@rollup/rollup-linux-arm-musleabihf@4.46.2':
    '@rollup/rollup-linux-arm-musleabihf': private
  '@rollup/rollup-linux-arm64-gnu@4.46.2':
    '@rollup/rollup-linux-arm64-gnu': private
  '@rollup/rollup-linux-arm64-musl@4.46.2':
    '@rollup/rollup-linux-arm64-musl': private
  '@rollup/rollup-linux-loongarch64-gnu@4.46.2':
    '@rollup/rollup-linux-loongarch64-gnu': private
  '@rollup/rollup-linux-ppc64-gnu@4.46.2':
    '@rollup/rollup-linux-ppc64-gnu': private
  '@rollup/rollup-linux-riscv64-gnu@4.46.2':
    '@rollup/rollup-linux-riscv64-gnu': private
  '@rollup/rollup-linux-riscv64-musl@4.46.2':
    '@rollup/rollup-linux-riscv64-musl': private
  '@rollup/rollup-linux-s390x-gnu@4.46.2':
    '@rollup/rollup-linux-s390x-gnu': private
  '@rollup/rollup-linux-x64-gnu@4.46.2':
    '@rollup/rollup-linux-x64-gnu': private
  '@rollup/rollup-linux-x64-musl@4.46.2':
    '@rollup/rollup-linux-x64-musl': private
  '@rollup/rollup-win32-arm64-msvc@4.46.2':
    '@rollup/rollup-win32-arm64-msvc': private
  '@rollup/rollup-win32-ia32-msvc@4.46.2':
    '@rollup/rollup-win32-ia32-msvc': private
  '@rollup/rollup-win32-x64-msvc@4.46.2':
    '@rollup/rollup-win32-x64-msvc': private
  '@types/babel__core@7.20.5':
    '@types/babel__core': private
  '@types/babel__generator@7.27.0':
    '@types/babel__generator': private
  '@types/babel__template@7.4.4':
    '@types/babel__template': private
  '@types/babel__traverse@7.28.0':
    '@types/babel__traverse': private
  '@types/estree@1.0.8':
    '@types/estree': private
  asynckit@0.4.0:
    asynckit: private
  browserslist@4.25.1:
    browserslist: private
  call-bind-apply-helpers@1.0.2:
    call-bind-apply-helpers: private
  caniuse-lite@1.0.30001731:
    caniuse-lite: private
  classnames@2.5.1:
    classnames: private
  combined-stream@1.0.8:
    combined-stream: private
  compute-scroll-into-view@3.1.1:
    compute-scroll-into-view: private
  convert-source-map@2.0.0:
    convert-source-map: private
  copy-to-clipboard@3.3.3:
    copy-to-clipboard: private
  csstype@3.1.3:
    csstype: private
  dayjs@1.11.13:
    dayjs: private
  debug@4.4.1:
    debug: private
  delayed-stream@1.0.0:
    delayed-stream: private
  dunder-proto@1.0.1:
    dunder-proto: private
  electron-to-chromium@1.5.195:
    electron-to-chromium: private
  es-define-property@1.0.1:
    es-define-property: private
  es-errors@1.3.0:
    es-errors: private
  es-object-atoms@1.1.1:
    es-object-atoms: private
  es-set-tostringtag@2.1.0:
    es-set-tostringtag: private
  esbuild@0.21.5:
    esbuild: private
  escalade@3.2.0:
    escalade: private
  follow-redirects@1.15.11:
    follow-redirects: private
  form-data@4.0.4:
    form-data: private
  fsevents@2.3.3:
    fsevents: private
  function-bind@1.1.2:
    function-bind: private
  gensync@1.0.0-beta.2:
    gensync: private
  get-intrinsic@1.3.0:
    get-intrinsic: private
  get-proto@1.0.1:
    get-proto: private
  gopd@1.2.0:
    gopd: private
  has-symbols@1.1.0:
    has-symbols: private
  has-tostringtag@1.0.2:
    has-tostringtag: private
  hasown@2.0.2:
    hasown: private
  is-mobile@5.0.0:
    is-mobile: private
  js-tokens@4.0.0:
    js-tokens: private
  jsesc@3.1.0:
    jsesc: private
  json2mq@0.2.0:
    json2mq: private
  json5@2.2.3:
    json5: private
  loose-envify@1.4.0:
    loose-envify: private
  lru-cache@5.1.1:
    lru-cache: private
  math-intrinsics@1.1.0:
    math-intrinsics: private
  mime-db@1.52.0:
    mime-db: private
  mime-types@2.1.35:
    mime-types: private
  ms@2.1.3:
    ms: private
  nanoid@3.3.11:
    nanoid: private
  node-releases@2.0.19:
    node-releases: private
  picocolors@1.1.1:
    picocolors: private
  postcss@8.5.6:
    postcss: private
  proxy-from-env@1.1.0:
    proxy-from-env: private
  rc-cascader@3.34.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1):
    rc-cascader: private
  rc-checkbox@3.5.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1):
    rc-checkbox: private
  rc-collapse@3.9.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1):
    rc-collapse: private
  rc-dialog@9.6.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1):
    rc-dialog: private
  rc-drawer@7.3.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1):
    rc-drawer: private
  rc-dropdown@4.2.1(react-dom@18.3.1(react@18.3.1))(react@18.3.1):
    rc-dropdown: private
  rc-field-form@2.7.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1):
    rc-field-form: private
  rc-image@7.12.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1):
    rc-image: private
  rc-input-number@9.5.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1):
    rc-input-number: private
  rc-input@1.8.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1):
    rc-input: private
  rc-mentions@2.20.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1):
    rc-mentions: private
  rc-menu@9.16.1(react-dom@18.3.1(react@18.3.1))(react@18.3.1):
    rc-menu: private
  rc-motion@2.9.5(react-dom@18.3.1(react@18.3.1))(react@18.3.1):
    rc-motion: private
  rc-notification@5.6.4(react-dom@18.3.1(react@18.3.1))(react@18.3.1):
    rc-notification: private
  rc-overflow@1.4.1(react-dom@18.3.1(react@18.3.1))(react@18.3.1):
    rc-overflow: private
  rc-pagination@5.1.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1):
    rc-pagination: private
  rc-picker@4.11.3(dayjs@1.11.13)(react-dom@18.3.1(react@18.3.1))(react@18.3.1):
    rc-picker: private
  rc-progress@4.0.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1):
    rc-progress: private
  rc-rate@2.13.1(react-dom@18.3.1(react@18.3.1))(react@18.3.1):
    rc-rate: private
  rc-resize-observer@1.4.3(react-dom@18.3.1(react@18.3.1))(react@18.3.1):
    rc-resize-observer: private
  rc-segmented@2.7.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1):
    rc-segmented: private
  rc-select@14.16.8(react-dom@18.3.1(react@18.3.1))(react@18.3.1):
    rc-select: private
  rc-slider@11.1.8(react-dom@18.3.1(react@18.3.1))(react@18.3.1):
    rc-slider: private
  rc-steps@6.0.1(react-dom@18.3.1(react@18.3.1))(react@18.3.1):
    rc-steps: private
  rc-switch@4.1.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1):
    rc-switch: private
  rc-table@7.51.1(react-dom@18.3.1(react@18.3.1))(react@18.3.1):
    rc-table: private
  rc-tabs@15.6.1(react-dom@18.3.1(react@18.3.1))(react@18.3.1):
    rc-tabs: private
  rc-textarea@1.10.2(react-dom@18.3.1(react@18.3.1))(react@18.3.1):
    rc-textarea: private
  rc-tooltip@6.4.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1):
    rc-tooltip: private
  rc-tree-select@5.27.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1):
    rc-tree-select: private
  rc-tree@5.13.1(react-dom@18.3.1(react@18.3.1))(react@18.3.1):
    rc-tree: private
  rc-upload@4.9.2(react-dom@18.3.1(react@18.3.1))(react@18.3.1):
    rc-upload: private
  rc-util@5.44.4(react-dom@18.3.1(react@18.3.1))(react@18.3.1):
    rc-util: private
  rc-virtual-list@3.19.1(react-dom@18.3.1(react@18.3.1))(react@18.3.1):
    rc-virtual-list: private
  react-is@18.3.1:
    react-is: private
  react-refresh@0.17.0:
    react-refresh: private
  resize-observer-polyfill@1.5.1:
    resize-observer-polyfill: private
  rollup@4.46.2:
    rollup: private
  scheduler@0.23.2:
    scheduler: private
  scroll-into-view-if-needed@3.1.0:
    scroll-into-view-if-needed: private
  semver@6.3.1:
    semver: private
  source-map-js@1.2.1:
    source-map-js: private
  string-convert@0.2.1:
    string-convert: private
  stylis@4.3.6:
    stylis: private
  throttle-debounce@5.0.2:
    throttle-debounce: private
  toggle-selection@1.0.6:
    toggle-selection: private
  update-browserslist-db@1.1.3(browserslist@4.25.1):
    update-browserslist-db: private
  yallist@3.1.1:
    yallist: private
included:
  dependencies: true
  devDependencies: true
  optionalDependencies: true
injectedDeps: {}
layoutVersion: 5
nodeLinker: isolated
packageManager: pnpm@9.3.0
pendingBuilds: []
prunedAt: Tue, 05 Aug 2025 13:49:29 GMT
publicHoistPattern:
  - '*eslint*'
  - '*prettier*'
registries:
  default: https://registry.npmjs.org/
skipped:
  - '@esbuild/aix-ppc64@0.21.5'
  - '@esbuild/aix-ppc64@0.25.8'
  - '@esbuild/android-arm64@0.21.5'
  - '@esbuild/android-arm64@0.25.8'
  - '@esbuild/android-arm@0.21.5'
  - '@esbuild/android-arm@0.25.8'
  - '@esbuild/android-x64@0.21.5'
  - '@esbuild/android-x64@0.25.8'
  - '@esbuild/darwin-arm64@0.21.5'
  - '@esbuild/darwin-arm64@0.25.8'
  - '@esbuild/darwin-x64@0.21.5'
  - '@esbuild/darwin-x64@0.25.8'
  - '@esbuild/freebsd-arm64@0.21.5'
  - '@esbuild/freebsd-arm64@0.25.8'
  - '@esbuild/freebsd-x64@0.21.5'
  - '@esbuild/freebsd-x64@0.25.8'
  - '@esbuild/linux-arm64@0.21.5'
  - '@esbuild/linux-arm64@0.25.8'
  - '@esbuild/linux-arm@0.21.5'
  - '@esbuild/linux-arm@0.25.8'
  - '@esbuild/linux-ia32@0.21.5'
  - '@esbuild/linux-ia32@0.25.8'
  - '@esbuild/linux-loong64@0.21.5'
  - '@esbuild/linux-loong64@0.25.8'
  - '@esbuild/linux-mips64el@0.21.5'
  - '@esbuild/linux-mips64el@0.25.8'
  - '@esbuild/linux-ppc64@0.21.5'
  - '@esbuild/linux-ppc64@0.25.8'
  - '@esbuild/linux-riscv64@0.21.5'
  - '@esbuild/linux-riscv64@0.25.8'
  - '@esbuild/linux-s390x@0.21.5'
  - '@esbuild/linux-s390x@0.25.8'
  - '@esbuild/linux-x64@0.21.5'
  - '@esbuild/linux-x64@0.25.8'
  - '@esbuild/netbsd-arm64@0.25.8'
  - '@esbuild/netbsd-x64@0.21.5'
  - '@esbuild/netbsd-x64@0.25.8'
  - '@esbuild/openbsd-arm64@0.25.8'
  - '@esbuild/openbsd-x64@0.21.5'
  - '@esbuild/openbsd-x64@0.25.8'
  - '@esbuild/openharmony-arm64@0.25.8'
  - '@esbuild/sunos-x64@0.21.5'
  - '@esbuild/sunos-x64@0.25.8'
  - '@esbuild/win32-arm64@0.21.5'
  - '@esbuild/win32-arm64@0.25.8'
  - '@esbuild/win32-ia32@0.21.5'
  - '@esbuild/win32-ia32@0.25.8'
  - '@rollup/rollup-android-arm-eabi@4.46.2'
  - '@rollup/rollup-android-arm64@4.46.2'
  - '@rollup/rollup-darwin-arm64@4.46.2'
  - '@rollup/rollup-darwin-x64@4.46.2'
  - '@rollup/rollup-freebsd-arm64@4.46.2'
  - '@rollup/rollup-freebsd-x64@4.46.2'
  - '@rollup/rollup-linux-arm-gnueabihf@4.46.2'
  - '@rollup/rollup-linux-arm-musleabihf@4.46.2'
  - '@rollup/rollup-linux-arm64-gnu@4.46.2'
  - '@rollup/rollup-linux-arm64-musl@4.46.2'
  - '@rollup/rollup-linux-loongarch64-gnu@4.46.2'
  - '@rollup/rollup-linux-ppc64-gnu@4.46.2'
  - '@rollup/rollup-linux-riscv64-gnu@4.46.2'
  - '@rollup/rollup-linux-riscv64-musl@4.46.2'
  - '@rollup/rollup-linux-s390x-gnu@4.46.2'
  - '@rollup/rollup-linux-x64-gnu@4.46.2'
  - '@rollup/rollup-linux-x64-musl@4.46.2'
  - '@rollup/rollup-win32-arm64-msvc@4.46.2'
  - '@rollup/rollup-win32-ia32-msvc@4.46.2'
  - fsevents@2.3.3
storeDir: E:\.pnpm-store\v3
virtualStoreDir: E:\desktop\file_upload\client\node_modules\.pnpm
virtualStoreDirMaxLength: 120
