{"name": "http-status-codes", "sideEffects": false, "version": "2.3.0", "description": "Constants enumerating the HTTP status codes. Based on the Java Apache HttpStatus API.", "scripts": {"update-codes": "ts-node --project ./scripts/tsconfig.json ./scripts/update-codes", "build": "./scripts/build.sh", "lint": "eslint .", "test": "jest ./src"}, "repository": {"type": "git", "url": "https://github.com/prettymuchbryce/http-status-codes.git"}, "files": ["build/", "LICENSE", "README.MD"], "main": "./build/cjs/index.js", "module": "./build/es/index.js", "keywords": ["node", "http", "apache", "HttpStatus", "httpclient", "status", "codes"], "author": "<PERSON>", "license": "MIT", "devDependencies": {"@types/fs-extra": "^9.0.1", "@types/jest": "^26.0.10", "@types/markdown-table": "^2.0.0", "@typescript-eslint/eslint-plugin": "^3.9.0", "@typescript-eslint/parser": "^3.9.1", "eslint": "^7.7.0", "eslint-config-airbnb-typescript": "^9.0.0", "eslint-plugin-import": "^2.22.0", "fs-extra": "^9.0.1", "jest": "^26.4.0", "markdown-table": "^2.0.0", "ts-jest": "^26.2.0", "ts-morph": "^7.3.0", "ts-node": "^9.0.0", "typescript": "^3.9.7"}}