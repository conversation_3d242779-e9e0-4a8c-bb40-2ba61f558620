export const FILE_SIZE = 1024 * 1024 * 1024 * 2;

// 根据文件类型设置不同的分片大小
export const CHUNK_SIZE = {
  IMAGE: 1024 * 1024 * 2, // 图片: 2MB
  VIDEO: 1024 * 1024 * 5, // 视频: 5MB
  DEFAULT: 1024 * 1024 * 1, // 默认: 1MB
};

// 获取适合的分片大小
export function getChunkSize(fileType) {
  if (fileType.startsWith('image/')) {
    return CHUNK_SIZE.IMAGE;
  } else if (fileType.startsWith('video/')) {
    return CHUNK_SIZE.VIDEO;
  } else {
    return CHUNK_SIZE.DEFAULT;
  }
}
